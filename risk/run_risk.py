import time

import pandas as pd
from tqsdk import BacktestFinished

from constant.constant import TQ_TIME_OUT, TIMEZONE_OFFSET_NS
from entity.trade_input import TradeInput
from entity.trade_result import TradeResult
from new_trade.base_trade_executor import BaseTradeExecutor
from new_trade.trade_strategy import TradeStrategy
from util.common_util import is_empty
from util.trade_util import get_time_price_list


# ToDo(hm): 实现重启任务，重新加载 instance，另外 instance 是不是应该实时的写到 run_config 中？
# ToDo(hm): 如果线上用这个该怎么修改呢？
# ToDo(hm): rename this
# ToDo(hm): 测试下断点续传
def run_risk(trade_input: TradeInput, api, trade_executor: BaseTradeExecutor) -> TradeResult:
    total_time_list = list()
    total_price_list = list()
    data_length = max([param.delta_time for param in trade_input.param_list]) + 1
    klines_init = api.get_kline_serial(trade_input.code, 1, data_length=data_length)
    time_list, price_list = get_time_price_list(klines_init)
    total_time_list.extend(time_list)
    total_price_list.extend(price_list)
    klines = api.get_kline_serial(trade_input.code, 1, data_length=1)
    trade_strategy = TradeStrategy(trade_executor, cd_ratio=trade_input.cd_ratio)
    # 记录所有开仓和平仓实例
    total_open_instances = list()
    total_close_instances = list()
    update_last_time = None
    try:
        while True:
            # 等待行情更新
            is_updated = api.wait_update(deadline=time.time() + TQ_TIME_OUT)
            if not is_updated:
                print("No update received within timeout period")
                continue

            if not api.is_changing(klines):
                continue

            # 获取当前K线时间
            kline_time = pd.to_datetime(klines.iloc[-1]['datetime'] + TIMEZONE_OFFSET_NS, unit='ns')

            if update_last_time is not None:
                time_delta = kline_time - update_last_time
                # 如果时间变化太小，跳过
                if time_delta.seconds < 1:
                    continue

            # 更新上次处理时间
            update_last_time = kline_time
            if kline_time.minute % 5 == 0 and kline_time.second == 0:
                print(f"kline_time={kline_time}")

            # ToDo(hm): 区分线上和测试
            # if kline_time.second % 5 == 0:
            #     print(f"kline_time={kline_time}")

            # 获取时间和价格列表
            # ToDo(hm): 这里其实不用 list，返回一个就行了，暂时先这样后面再改
            time_list, price_list = get_time_price_list(klines)
            if len(time_list) == 0 or len(price_list) == 0:
                print("Warning: Empty time or price list")
                continue

            # 存储所有时间价格数据
            if is_empty(total_time_list):
                total_time_list += time_list
                total_price_list += price_list
            else:
                for idx in range(len(time_list)):
                    # 找到第一个比 total_time_list[-1] 大的时间，并追加
                    if time_list[idx] > total_time_list[-1]:
                        total_time_list.extend(time_list[idx:])
                        total_price_list.extend(price_list[idx:])
                        break
                else:
                    # 没有更新继续等下一轮数据
                    continue

            # 尝试开仓
            open_attempt_instance_list = trade_strategy.attempt_open_instance(trade_input.param_list, total_price_list,
                                                                              total_time_list)
            if open_attempt_instance_list:
                total_open_instances.extend(open_attempt_instance_list)
            # 尝试收集 37% rule 的信息
            # ToDo(hm): 记得测试断点续传的问题
            trade_strategy.attempt_collect_37_rule_info(trade_input.param_list, total_price_list, total_time_list)
            # 尝试平仓
            close_attempt_instance_list = trade_strategy.attempt_close_instance(trade_input.param_list,
                                                                                total_price_list,
                                                                                total_time_list)
            if close_attempt_instance_list:
                total_close_instances.extend(close_attempt_instance_list)
        return TradeResult(trade_input.param_list, total_time_list, total_price_list)
    except KeyboardInterrupt:
        print("Trading interrupted by user")
    except BacktestFinished:
        print("Backtest finished")
        return TradeResult(trade_input.param_list, total_time_list, total_price_list)

    finally:
        # 关闭API连接
        api.close()
