import os
import sys
from datetime import datetime
from multiprocessing import Process

import pandas as pd

from TqMockApi import TqMockApi
from constant.constant import CONFIG_FILE_NAME, RUN_NUM, STATUS, GOING_ON, PID, PARAM_DF_FILE_NAME, CAT, \
    ACCOUNT, CODE, USE_API_CODE, DRY_RUN, PARAM_VERSION, BACKTEST_START_TIME, BACKTEST_END_TIME, \
    PRICE_TIME_SEQ_FILE_NAME, RUN_LOG_FILE_NAME, BACKTEST_CHECKPOINT_DIR, TIME_FORMAT, \
    CHECKPOINT_DIR, READABLE_TIME_FORMAT, TOTAL_FUND_FOR_ALL_INSTRUCT, TASK_START_TIME, PENDING, VAR_DIR, \
    GLOBAL_CONFIG_FILE_NAME, ACCOUNT_CONFIG_FILE_NAME, ACCOUNT_NAME_LIST, CHO<PERSON>E_CAT_LIST, TASK_UPDATE_TIME, PATH, \
    INIT_FUND, OPEN_ONCE_MAX_FUND, MESSAGE, GOING_ON_OVER, SUCCESS, SUCCESS_OVER, PARAM_INSTANCE_CHART_FILE_NAME, \
    TIME_COL, PRICE_COL, PARAM_INSTANCE_DF_FILE_NAME, FAILED, ACCOUNT_T, PASSWORD_T, TRADE_DAY, PARAM_INSTANCE_DIR
from entity.param_instance import ParamInstance
from entity.prepare_checkpoint_param import PrepareCheckpointParam
from entity.trade_input import TradeInput
from entity.enum.trade_mode import TradeMode
from entity.param import Param
from loader.file_loader import load_config
from new_trade.mock_trade_executor import MockTradeExecutor
from new_trade.tq_trade_executor import TqTradeExecutor
from new_trade.visualize import draw_line
from risk.run_risk import run_risk
from util.common_util import get_code_version, is_empty, get_caller, print_warn, not_empty
from util.date_util import get_cur_time_str, get_trade_day_str
from util.email_util import send_warn_email
from util.encrypt_util import decrypt
from util.file_util import join_path, load_json, update_json_file, mkdir_if_not_exists, is_invalid_path, \
    get_absolute_path, get_relative_path, dump_json, copy_to_dir, get_param_root_dir, get_latest_param_dir, \
    is_valid_path, list_dir_filter_dot
from util.process_util import FilePrintRunner, is_process_running
from util.tq_util import get_code_from_tq, get_tq_api
from util.trade_util import get_instance_list, merge_to_instance_df

try:
    from tqsdk2 import TqApi, TqAuth, TqSim, TqBacktest, TargetPosTask, BacktestFinished, TqAccount
except ImportError:
    try:
        from tqsdk import TqApi, TqAuth, TqSim, TqBacktest, TargetPosTask, BacktestFinished, TqAccount
    except ImportError:
        print("Error: Neither tqsdk2 nor tqsdk could be imported. Please install one of them.")
        sys.exit(1)


def get_ckp_path(data_dir, trade_day_str, now, cat, dry_run, account_name):
    if dry_run:
        return join_path(data_dir, BACKTEST_CHECKPOINT_DIR, account_name, cat, trade_day_str,
                         now.strftime(TIME_FORMAT))

    return join_path(data_dir, CHECKPOINT_DIR, account_name, cat, trade_day_str, now.strftime(TIME_FORMAT))


def prepare(data_dir, trade_day_str, now, cat, dry_run, ckp_path, backtest_start_time_str, backtest_end_time_str,
            use_backtest_param, account_name):
    mkdir_if_not_exists(ckp_path)
    prepare_param = PrepareCheckpointParam()
    prepare_param.cat = cat
    cat_param_dir = join_path(get_param_root_dir(data_dir, use_backtest_param, account_name), cat)
    prepare_param.meta_param_path = get_latest_param_dir(cat_param_dir)
    if is_invalid_path(prepare_param.meta_param_path):
        print(f"跳过尚未配置的品种：{cat}")
        return False

    use_backtest_param_suffix = "（回测参数）" if use_backtest_param else ""
    print(f"使用的参数={os.path.basename(prepare_param.meta_param_path)}" + use_backtest_param_suffix)
    prepare_param.start_time_str = now.strftime(READABLE_TIME_FORMAT)
    prepare_param.checkpoint_path = get_relative_path(ckp_path, data_dir)
    prepare_param.trade_day_str = trade_day_str
    prepare_param.dry_run = dry_run
    if dry_run:
        # ToDo(hm): 回测时间不能跨交易日
        prepare_param.backtest_start_time_str = backtest_start_time_str
        prepare_param.backtest_end_time_str = backtest_end_time_str
    prepare_checkpoint(prepare_param, data_dir)
    return True


def prepare_checkpoint(prepare_param: PrepareCheckpointParam, data_dir):
    checkpoint_path = get_absolute_path(prepare_param.checkpoint_path, data_dir)
    meta_param = load_config(prepare_param.meta_param_path)
    if is_empty(meta_param):
        raise NotImplementedError(f"记得实现这里: {get_caller()}")

    account_name = meta_param[ACCOUNT]
    global_config_path = join_path(data_dir, VAR_DIR, account_name, GLOBAL_CONFIG_FILE_NAME)
    if is_invalid_path(global_config_path):
        raise RuntimeError(f"{global_config_path} not found")

    global_config_dict = load_json(global_config_path, with_lock=True, data_dir=data_dir)
    if TOTAL_FUND_FOR_ALL_INSTRUCT not in global_config_dict:
        raise RuntimeError(f"{TOTAL_FUND_FOR_ALL_INSTRUCT} not found in {GLOBAL_CONFIG_FILE_NAME}")

    total_fund_for_all_instruct = global_config_dict[TOTAL_FUND_FOR_ALL_INSTRUCT]
    # copy param df
    param_df_path = join_path(prepare_param.meta_param_path, PARAM_DF_FILE_NAME)
    copy_to_dir(param_df_path, checkpoint_path)

    # copy account info from var dir
    var_dir = join_path(data_dir, VAR_DIR, account_name)
    account_config_path = join_path(var_dir, ACCOUNT_CONFIG_FILE_NAME)
    copy_to_dir(account_config_path, checkpoint_path)

    # other config
    run_config_dict = {
        TOTAL_FUND_FOR_ALL_INSTRUCT: total_fund_for_all_instruct,
        TASK_START_TIME: prepare_param.start_time_str,
        STATUS: PENDING,
        DRY_RUN: prepare_param.dry_run,
        RUN_NUM: 0,
        BACKTEST_START_TIME: prepare_param.backtest_start_time_str,
        BACKTEST_END_TIME: prepare_param.backtest_end_time_str,
        TRADE_DAY: prepare_param.trade_day_str,
    }
    for key in meta_param:
        run_config_dict[key] = meta_param[key]
    config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
    dump_json(run_config_dict, config_path)


def linear_start_backtest_process(data_dir, account_cat_list, project_dir, backtest_start_time_str,
                                  backtest_end_time_str, use_backtest_param, send_email):
    ckp_path_list = list()
    now = datetime.now()
    for d in account_cat_list:
        cat = d[CAT]
        account_name = d[ACCOUNT]
        trade_day_str = get_trade_day_str(pd.to_datetime(backtest_start_time_str))
        ckp_path = get_ckp_path(data_dir, trade_day_str, now, cat, True, account_name)
        prepare_success = prepare(data_dir, trade_day_str, now, cat, True, ckp_path, backtest_start_time_str,
                                  backtest_end_time_str, use_backtest_param, account_name)
        if not prepare_success:
            continue

        ckp_path_list.append(ckp_path)
        print(f"✅已开启风控：{cat}")
    if is_empty(ckp_path_list):
        print(f"没有可执行的风控任务，请检查参数是否正确配置")
        return

    log_path = join_path(ckp_path_list[0], RUN_LOG_FILE_NAME)
    runner = FilePrintRunner(linear_adapt_to_run_risk, log_path)
    process = Process(target=runner, args=(ckp_path_list, data_dir, project_dir, send_email))
    process.start()
    print(f"线性执行风控任务，所有日志记录在第一个品种中：{log_path}")


# linear run backtest risk tasks
def linear_adapt_to_run_risk(checkpoint_path_list, data_dir, project_dir, send_email=False):
    # compare_result_list = list()
    # should_send_warn = False
    for checkpoint_path in checkpoint_path_list:
        # ToDo(hm): use_price_seq_file=True
        trade_result = adapt_to_run_risk(checkpoint_path, data_dir, project_dir, send_email)
        all_instances = get_instance_list(trade_result.param_list)
        actual_instance_df = merge_to_instance_df(instance_list=all_instances)
        if not_empty(actual_instance_df):
            save_path = join_path(checkpoint_path, PARAM_INSTANCE_DF_FILE_NAME)
            actual_instance_df.to_csv(save_path, index=False)
        save_path = join_path(checkpoint_path, PARAM_INSTANCE_CHART_FILE_NAME)
        draw_line(trade_result.price_list, instances=all_instances, time_list=trade_result.time_list,
                  save_path=save_path)
        price_seq_df = pd.DataFrame({TIME_COL: trade_result.time_list, PRICE_COL: trade_result.price_list})
        save_path = join_path(checkpoint_path, PRICE_TIME_SEQ_FILE_NAME)
        price_seq_df.to_csv(save_path, index=False)
        # compare
        # config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
        # run_config_dict = load_json(config_path)
        # backtest_start_time_str = run_config_dict[BACKTEST_START_TIME]
        # backtest_trade_dy_str = get_trade_day_str(pd.to_datetime(backtest_start_time_str))
        # account_name = run_config_dict[ACCOUNT]
        # record_dir = join_path(data_dir, RECORD_DIR, account_name)
        # cat = get_key_by_val(RISK_CAT_DICT, run_config_dict[CAT])
        # trade_day_to_df_dict_backtest = get_record_df_by_trade_day(record_dir, cat, True, print_load_info=True)
        # trade_day_to_df_dict_prod = get_record_df_by_trade_day(record_dir, cat, False, print_load_info=True)
        # is_equal, msg = compare_risk_record(trade_day_to_df_dict_prod, trade_day_to_df_dict_backtest,
        #                                     backtest_trade_dy_str)
        # result = f"品种{cat}在交易日{backtest_trade_dy_str}回测结果和线上"
        # if is_equal:
        #     result += "一致"
        # else:
        #     result += "不同：" + msg
        #     should_send_warn = True
        # print(result)
        # compare_result_list.append(result)
    if not send_email:
        return

    # log_path = join_path(checkpoint_path_list[0], RUN_LOG_FILE_NAME)
    # compare_result_list.append(f"运行日志参见：{log_path}")
    # content = "\n".join(compare_result_list)
    # if should_send_warn:
    #     send_warn_email(data_dir, "风控回测结果不一致", content)
    # else:
    #     send_info_email(data_dir, "风控回测结果一致", content)


def adapt_to_run_risk(checkpoint_path, data_dir, project_dir=None, send_email=True,
                      use_price_seq_file=False):
    config_path = join_path(checkpoint_path, CONFIG_FILE_NAME)
    run_config_dict = load_json(config_path)
    run_num = run_config_dict[RUN_NUM] + 1 if RUN_NUM in run_config_dict else 1
    # ToDo(hm): not PENDING, should add more status
    update_json_file(config_path, {STATUS: GOING_ON, PID: os.getpid(), RUN_NUM: run_num})
    account_name = run_config_dict[ACCOUNT]
    cat = run_config_dict[CAT]
    code = run_config_dict[CODE]
    trade_day_str = run_config_dict[TRADE_DAY]
    param_df_path = join_path(checkpoint_path, PARAM_DF_FILE_NAME)
    param_df = pd.read_csv(param_df_path)
    param_list = list()
    param_version = run_config_dict[PARAM_VERSION]
    for row_idx, row in param_df.iterrows():
        param_code = f"{param_version}_{row_idx}"
        param = Param(
            code=param_code,
            cat=cat,
            account_name=account_name,
            contract_code=code,
            delta_time=row["tr"],
            # delta_percent 实际上要 /100
            delta_percent=row["dp"] / 100,
            alpha=row["al"],
            trade_mode=TradeMode(row["tm"])
        )
        param_list.append(param)
        # 加载对应的 param instance，考虑跨周末和节假日的情况
        # data_dir/param_instance/account_name/cat/trade_day/param_code/<instance_code>.json
        instance_dir = join_path(data_dir, PARAM_INSTANCE_DIR, account_name, cat, trade_day_str, param_code)
        if is_valid_path(instance_dir):
            instance_file_list = list_dir_filter_dot(instance_dir)
            for instance_file in instance_file_list:
                instance_path = join_path(instance_dir, instance_file)
                instance_dict = load_json(instance_path)
                instance = ParamInstance.from_dict(instance_dict)
                param.instance_list.append(instance)
    account_config_path = join_path(checkpoint_path, ACCOUNT_CONFIG_FILE_NAME)
    if is_invalid_path(account_config_path):
        err_msg = f"账号{account_name}的账密文件加载失败：{account_config_path}"
        print_warn(err_msg)
        update_json_file(config_path,
                         {STATUS: FAILED, TASK_UPDATE_TIME: get_cur_time_str(READABLE_TIME_FORMAT),
                          MESSAGE: err_msg})
        if send_email:
            send_warn_email(data_dir, err_msg, content=f"代码版本: {get_code_version(project_dir)}")
        return

    account_dict = load_json(account_config_path)
    if USE_API_CODE in run_config_dict and run_config_dict[USE_API_CODE]:
        try:
            code = get_code_from_tq(data_dir, account_name, cat)
        except RuntimeError as e:
            err_msg = f"账号{account_name}的{cat}{code}风控告警: API 获取合约代码失败，将会使用默认合约{code}"
            if send_email:
                send_warn_email(data_dir, err_msg, content=f"代码版本: {get_code_version(project_dir)}")
    if run_config_dict[DRY_RUN]:
        # 回测
        return dry_run_risk(data_dir, code, param_list, run_config_dict, checkpoint_path, use_price_seq_file,
                            account_dict)

    # 线上
    # ToDo(hm): how to set init_fund?
    trade_input = TradeInput(param_list, code, user_name=decrypt(account_dict[ACCOUNT_T]),
                             password=decrypt(account_dict[PASSWORD_T]), init_fund=********)
    api = get_tq_api(data_dir, account_name)
    position = api.get_position(trade_input.code)
    trade_executor = TqTradeExecutor(data_dir, api, position)
    return run_risk(trade_input, api, trade_executor)


def dry_run_risk(data_dir, code, param_list, run_config_dict, checkpoint_path, use_price_seq_file, account_dict):
    backtest_start_time = run_config_dict[BACKTEST_START_TIME]
    backtest_end_time = run_config_dict[BACKTEST_END_TIME]
    # ToDo(hm): how to set init_fund?
    trade_input = TradeInput(param_list, code, user_name=decrypt(account_dict[ACCOUNT_T]),
                             password=decrypt(account_dict[PASSWORD_T]), backtest_start_time=backtest_start_time,
                             backtest_end_time=backtest_end_time, init_fund=********)
    start_time = pd.to_datetime(trade_input.backtest_start_time)
    end_time = pd.to_datetime(trade_input.backtest_end_time)
    # 现在支持两种回测方式：一个是使用价格序列文件，一个是使用天勤API
    # use mock api
    if use_price_seq_file:
        price_seq_path = join_path(checkpoint_path, PRICE_TIME_SEQ_FILE_NAME)
        api = TqMockApi(data_path=price_seq_path, start_dt=start_time, end_dt=end_time)
        trade_executor = MockTradeExecutor(data_dir, 100)
    # use tq api
    else:
        tqacc = TqSim(trade_input.init_fund)
        api = TqApi(tqacc, backtest=TqBacktest(start_dt=start_time, end_dt=end_time),
                    auth=TqAuth(trade_input.user_name, trade_input.password), web_gui=False)
        position = api.get_position(trade_input.code)
        trade_executor = TqTradeExecutor(data_dir, api, position)
    trade_result = run_risk(trade_input, api, trade_executor)
    return trade_result


def load_latest_account_cat_info_dict(data_dir, dry_run):
    account_cat_info_dict = dict()
    for account_name in ACCOUNT_NAME_LIST:
        account_cat_info_dict[account_name] = dict()
        for cat in CHOOSE_CAT_LIST:
            # every cat only run one process at the same time, so just find the latest one
            # /checkpoint/<account>/<cat>/<run_date_str>/<run_time_str>
            cur_process_info = get_latest_process_info(data_dir, account_name, cat, dry_run)
            account_cat_info_dict[account_name][cat] = cur_process_info
    return account_cat_info_dict


def get_latest_process_info(data_dir, account_name, cat, dry_run):
    cur_process_info = {
        ACCOUNT: account_name,
        CAT: cat,
        PID: None,
        CODE: None,
        STATUS: '',
        TASK_START_TIME: None,
        TASK_UPDATE_TIME: None,
        PATH: None,
        PARAM_VERSION: None,
        # RISK_START_TIME_LIST: None,
        INIT_FUND: None,
        OPEN_ONCE_MAX_FUND: None,
        TOTAL_FUND_FOR_ALL_INSTRUCT: None,
        DRY_RUN: None,
        RUN_NUM: None,
        MESSAGE: None,
    }
    ckp_path = get_latest_ckp_path(data_dir, account_name, cat, dry_run)
    cur_process_info[PATH] = ckp_path
    if is_invalid_path(ckp_path):
        return cur_process_info

    try:
        run_config_dict = load_config(ckp_path)
    except Exception as e:
        run_config_dict = None
    if is_empty(run_config_dict):
        return cur_process_info

    for key in cur_process_info:
        if key in run_config_dict:
            cur_process_info[key] = run_config_dict[key]

    cur_process_info[STATUS] = run_config_dict[STATUS]
    if PID in run_config_dict:
        cur_process_info[PID] = run_config_dict[PID]
        # update status by actual
        cur_process_info[STATUS] = get_actual_status(run_config_dict[STATUS], run_config_dict[PID])
        return cur_process_info

    return cur_process_info


def get_actual_status(raw_status, pid):
    is_running = is_process_running(pid)
    if raw_status == GOING_ON:
        if is_running:
            return GOING_ON

        return GOING_ON_OVER

    if raw_status == SUCCESS:
        if is_running:
            return SUCCESS

        return SUCCESS_OVER

    return raw_status


def get_latest_ckp_path(data_dir, account_name, cat, dry_run):
    if dry_run:
        cat_dir = join_path(data_dir, BACKTEST_CHECKPOINT_DIR, account_name, cat)
    else:
        cat_dir = join_path(data_dir, CHECKPOINT_DIR, account_name, cat)
    if is_invalid_path(cat_dir):
        return None

    run_date_list = os.listdir(cat_dir)
    if is_empty(run_date_list):
        return None

    latest_run_date = sorted(run_date_list)[-1]
    latest_run_date_dir = join_path(cat_dir, latest_run_date)
    run_time_list = os.listdir(latest_run_date_dir)
    if is_empty(run_time_list):
        return None

    latest_run_time = sorted(run_time_list)[-1]
    return join_path(latest_run_date_dir, latest_run_time)
