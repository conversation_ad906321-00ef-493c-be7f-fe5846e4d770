import datetime
import json
from dataclasses import dataclass

from constant.constant import READABLE_TIME_FORMAT
from entity.enum.status import Status
from entity.enum.direction import Direction
from entity.enum.trade_mode import TradeMode
from util.common_util import to_dict


@dataclass
class ParamInstance:
    code: str
    cat: str
    account_name: str
    contract_code: str
    param_code: str
    direction: Direction
    status: Status
    open_price: int
    open_time: datetime.datetime
    expected_close_price: int
    actual_close_price: int = None
    close_time: datetime.datetime = None
    profit: int = 0
    # 开仓量，使用int类型保持与项目一致
    open_volume: int = 0
    # 开仓前的价格，即start_price，使用int类型保持与项目一致
    pre_price: int = None
    # 交易模式，来自于创建时对应的Param
    trade_mode: TradeMode = None
    # 时间范围，来自于创建时对应的Param
    delta_time: int = None
    # 百分比变化，来自于创建时对应的Param
    delta_percent: float = None
    # 平仓系数，来自于创建时对应的Param
    alpha: float = None
    # 观察期结束时间（包含），用于判断是否在观察期内
    observe_end_time: datetime.datetime = None
    # 观察期内的最佳价格
    best_price_so_far: int = None

    def __repr__(self):
        return json.dumps(to_dict(self), indent=2, ensure_ascii=False)

    def __eq__(self, other):
        if not isinstance(other, ParamInstance):
            return False

        # 比较 direction
        if self.direction != other.direction:
            return False

        # 比较 status
        if self.status != other.status:
            return False

        # 比较 open_price
        if self.open_price != other.open_price:
            return False

        # 比较 open_time (忽略微秒和时区)
        # 将两个时间对象转换为字符串进行比较，以避免类型和时区的问题
        self_time_str = self.open_time.strftime("%Y-%m-%d %H:%M:%S")
        other_time_str = other.open_time.strftime("%Y-%m-%d %H:%M:%S")
        if self_time_str != other_time_str:
            print(f"open_time: {self.open_time} != {other.open_time}")
            print(f"self_time_str: {self_time_str}, other_time_str: {other_time_str}")
            return False

        # 比较 expected_close_price
        if self.expected_close_price != other.expected_close_price:
            return False

        # 比较 pre_price
        if self.pre_price != other.pre_price:
            return False

        return True

    def __hash__(self):
        """生成对象的哈希值。
        由于我们重写了 __eq__ 方法，也需要重写 __hash__ 方法。
        哈希值基于我们用于比较的相同字段。

        Returns:
            int: 对象的哈希值
        """
        # 使用元组的哈希值，元组包含我们用于比较的所有字段
        # 对于 open_time，使用字符串表示以保持一致性
        return hash((self.direction,
                     self.status,
                     self.open_price,
                     self.open_time.strftime("%Y-%m-%d %H:%M:%S"),
                     self.expected_close_price,
                     self.pre_price))

    @classmethod
    def from_dict(cls, data):
        if data is None:
            raise ValueError("Input data is None")

        # 解析 datetime 字符串
        # 使用 pandas 的 to_datetime 来解析时间，并设置时区为 Asia/Shanghai
        try:
            import pandas as pd
            # 添加错误处理
            if 'open_time' not in data or data['open_time'] is None:
                raise ValueError(f"Missing open_time in data: {data}")

            # open_time = pd.to_datetime(data['open_time']).tz_localize('Asia/Shanghai')
            open_time = pd.to_datetime(data['open_time'])
            close_time = None
            if "close_time" in data and data['close_time'] is not None:
                # close_time = pd.to_datetime(data['close_time']).tz_localize('Asia/Shanghai')
                close_time = pd.to_datetime(data['close_time'])

            observe_end_time = None
            if "observe_end_time" in data and data['observe_end_time'] is not None:
                observe_end_time = pd.to_datetime(data['observe_end_time'])
        except (ImportError, ValueError) as e:
            print(f"Error using pandas: {e}. Falling back to datetime.")
            # 如果没有 pandas 或者出错，则使用 datetime
            if 'open_time' not in data or data['open_time'] is None:
                raise ValueError(f"Missing open_time in data: {data}")

            open_time = datetime.datetime.strptime(data['open_time'], READABLE_TIME_FORMAT)
            close_time = None
            if "close_time" in data and data['close_time'] is not None:
                close_time = datetime.datetime.strptime(data['close_time'], READABLE_TIME_FORMAT)
            observe_end_time = None
            if "observe_end_time" in data and data['observe_end_time'] is not None:
                observe_end_time = datetime.datetime.strptime(data['observe_end_time'], READABLE_TIME_FORMAT)

        # 解析枚举值
        direction = Direction(data['direction'])
        status = Status(data['status'])
        trade_mode = TradeMode(data['trade_mode'])
        return cls(
            code=data['code'],
            cat=data['cat'],
            account_name=data['account_name'],
            contract_code=data['contract_code'],
            param_code=data['param_code'],
            direction=direction,
            status=status,
            open_price=data['open_price'],
            open_time=open_time,
            expected_close_price=data.get('expected_close_price'),
            actual_close_price=data.get('actual_close_price'),
            close_time=close_time,
            profit=data.get('profit', 0),
            open_volume=data.get('open_volume', 0),
            pre_price=data.get('pre_price'),
            trade_mode=trade_mode,
            delta_time=data.get('delta_time'),
            delta_percent=data.get('delta_percent'),
            alpha=data.get('alpha'),
            observe_end_time=observe_end_time,
            best_price_so_far=data.get('best_price_so_far')
        )
