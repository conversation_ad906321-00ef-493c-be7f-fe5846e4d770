from typing import List

import pandas as pd

from constant.constant import TIMEZONE_OFFSET_NS
from entity.param_instance import ParamInstance
from entity.param import Param
from util.common_util import to_dict, is_empty
from util.date_util import get_trade_day_str
from util.df_util import put_cols_ahead


def merge_to_instance_df(instance_list):
    if is_empty(instance_list):
        return None

    instance_df = pd.DataFrame([to_dict(i) for i in instance_list])
    important_cols = ["open_time", "close_time", "cat", "code", "direction", "status", "open_price"]
    instance_df = put_cols_ahead(instance_df, important_cols)
    return instance_df.sort_values(by=important_cols)


def get_instance_list(param_list: List[Param]) -> List[ParamInstance]:
    """
    获取所有参数的交易实例列表

    Args:
        param_list: 参数列表

    Returns:
        所有交易实例列表
    """
    instance_list = []
    for param in param_list:
        instance_list.extend(param.instance_list)
    return instance_list

# ToDo(hm): 这个转换比较耗时，优化一下
# ToDo(hm): 只在初始化的时候拉所有的数据，后面只要递增就行
# ToDo(hm): 内部在计算的时候直接用原始的时间戳，而不是转成可读的时间
def get_time_price_list(klines):
    # 转换时间戳为可读的日期时间格式，并调整时区
    klines["datetime_readable"] = pd.to_datetime(klines["datetime"] + TIMEZONE_OFFSET_NS, unit='ns')
    # 添加交易日字段
    klines["trade_day_str"] = klines["datetime_readable"].apply(get_trade_day_str)
    # 获取最新K线的交易日
    cur_trade_day_str = klines.iloc[-1]["trade_day_str"]

    # 过滤出当前交易日的数据
    # 注意：在回测模式下，可能需要保留所有数据，取决于回测的需求
    filtered_klines = klines[klines["trade_day_str"] == cur_trade_day_str]

    # 如果过滤后没有数据，则使用原始数据（避免在回测中丢失数据）
    if len(filtered_klines) == 0:
        print("Warning: No data for current trading day, using all available data")
        filtered_klines = klines

    # 返回时间列表和价格列表（价格转换为整数）
    time_list = list(filtered_klines["datetime_readable"])
    # 将价格转换为整数
    price_list = [int(price) for price in filtered_klines["open"]]
    return time_list, price_list
