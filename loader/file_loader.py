import os

import pandas as pd

from constant.constant import CONFIG_FILE_NAME
from util.file_util import is_invalid_path, load_json


def load_checkpoint_json(checkpoint_path, json_file_name):
    if is_invalid_path(checkpoint_path):
        # print(f"load_checkpoint_json 传入的 checkpoint_path 为 None")
        return dict()

    json_file_path = os.path.join(checkpoint_path, json_file_name)
    if is_invalid_path(json_file_path):
        print(f"load_checkpoint_json 传入的 json_file_path 不存在: {json_file_path}")
        return dict()

    return load_json(json_file_path)


def load_config(checkpoint_path):
    return load_checkpoint_json(checkpoint_path, CONFIG_FILE_NAME)

def load_instance_df(path):
    # 读入 "" 转成 None 而不是 nan
    df = pd.read_csv(path, na_filter=False)
    return df.replace('', None)
