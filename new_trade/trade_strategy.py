import datetime
from typing import List

from entity.enum.status import Status
from entity.enum.direction import Direction
from entity.enum.trade_mode import TradeMode
from entity.param_instance import ParamInstance
from entity.param import Param
from new_trade.base_trade_executor import BaseTradeExecutor
from util.date_util import get_cur_time_str


class TradeStrategy:
    """
    交易策略类，封装了交易逻辑，包括开仓、平仓等操作
    """

    def __init__(self, trade_executor: BaseTradeExecutor, cd_ratio: float = 1.0, max_volume_per_trade: int = None):
        """
        初始化交易策略

        Args:
            trade_executor: 交易执行器
            cd_ratio: 冷却时间比例，默认为1.0
            max_volume_per_trade: 单次开仓最大手数，如果为None则使用默认值
        """
        self.trade_executor = trade_executor
        self.cd_ratio = cd_ratio

    def trade(self, param_list: List[Param], price_list, time_list):
        """
        执行交易策略

        Args:
            param_list: 参数列表
            price_list: 价格列表
            time_list: 时间列表

        Returns:
            所有开仓和平仓的实例列表
        """
        all_open_instances = []
        all_close_instances = []

        for cur_idx in range(len(price_list)):
            # 对于每一时刻
            # 先尝试开仓
            open_attempt_instance_list = self.attempt_open_instance(
                param_list, price_list, time_list, cur_idx
            )
            if open_attempt_instance_list:
                all_open_instances.extend(open_attempt_instance_list)

            # 再尝试平仓
            close_attempt_instance_list = self.attempt_close_instance(
                param_list, price_list, time_list, cur_idx
            )
            if close_attempt_instance_list:
                all_close_instances.extend(close_attempt_instance_list)

        return all_open_instances, all_close_instances

    def attempt_collect_37_rule_info(self, param_list: List[Param], price_list, time_list, cur_idx=None):
        if cur_idx is None:
            cur_idx = len(price_list) - 1
        cur_price = int(price_list[cur_idx])
        cur_time = time_list[cur_idx]
        for param in param_list:
            # 只有 37% rule 的模式才需要更新 best_price_so_far
            if param.trade_mode not in [TradeMode.SAME_TREND_STOP_PROFIT_WITH_37_RULE,
                                        TradeMode.REVERSE_TREND_STOP_PROFIT_WITH_37_RULE]:
                continue

            for param_instance in param.instance_list:
                if param_instance.status != Status.OPEN_SUCCESS:
                    continue

                if cur_time > param_instance.observe_end_time:
                    continue

                if param_instance.direction == Direction.UP and cur_price > param_instance.best_price_so_far:
                    param_instance.best_price_so_far = cur_price
                    self.trade_executor.upsert_instance(param_instance)
                    continue

                if param_instance.direction == Direction.DOWN and cur_price < param_instance.best_price_so_far:
                    param_instance.best_price_so_far = cur_price
                    self.trade_executor.upsert_instance(param_instance)

    def attempt_open_instance(self, param_list: List[Param], price_list, time_list, cur_idx=None):
        if cur_idx is None:
            cur_idx = len(price_list) - 1
        cur_price = int(price_list[cur_idx])
        cur_time = time_list[cur_idx]
        open_attempt_instance_list = list()
        for param in param_list:
            if param.next_open_time is not None and param.next_open_time > cur_time:
                # 仍在冷却期内，跳过
                continue

            # 直接使用索引差来计算起始时间
            # 确保索引差大于等于delta_time
            start_time_idx = cur_idx - param.delta_time

            # 如果起始索引小于0，则无法满足时间差要求
            if start_time_idx < 0:
                continue

            # 计算价格变化百分比
            start_price = int(price_list[start_time_idx])
            actual_delta_percent = (cur_price - start_price) / start_price

            # 根据交易模式判断是否满足开仓条件
            direction, expected_end_price = self._calculate_trade_direction_and_price(
                param, cur_price, actual_delta_percent
            )
            if direction is None:
                continue

            instance_code = get_cur_time_str()
            instance = ParamInstance(
                code=instance_code,
                cat=param.cat,
                account_name=param.account_name,
                # 使用 param的 contract_code
                contract_code=param.contract_code,
                # 使用 param 的 code 作为 param_code
                param_code=param.code,
                direction=direction,
                status=Status.OPEN_ATTEMPT,
                open_price=cur_price,
                expected_close_price=expected_end_price,
                open_time=cur_time,
                # 开仓量初始化为0，将在执行开仓时设置
                open_volume=0,
                # 记录开仓前的价格，即start_price
                pre_price=start_price,
                # 记录交易模式，来自于创建时对应的Param
                trade_mode=param.trade_mode,
                # 从Param拷贝三个新字段
                delta_time=param.delta_time,
                delta_percent=param.delta_percent,
                alpha=param.alpha
            )
            # 执行开仓
            if self._execute_open_trade(param, instance):
                open_attempt_instance_list.append(instance)
        return open_attempt_instance_list

    def _execute_open_trade(self, param: Param, instance: ParamInstance) -> bool:
        # 计算开仓量，传入品种代码以获取对应的保证金要求
        open_volume = self.trade_executor.get_open_volume(param.cat)
        if open_volume <= 0:
            return False

        # 设置开仓量
        instance.open_volume = open_volume
        # 执行开仓
        self.trade_executor.open(instance)

        # 检查开仓是否成功
        if instance.status != Status.OPEN_SUCCESS:
            print(f"开仓失败: {instance.code}, 状态: {instance.status}")
            return False

        param.instance_list.append(instance)
        # 更新资金，减去开仓所需的保证金
        self.trade_executor.update_total_fund_after_open(param.cat, open_volume)
        # 更新下一次开仓时间
        cooldown_seconds = param.delta_time * self.cd_ratio
        param.next_open_time = instance.open_time + datetime.timedelta(seconds=cooldown_seconds)
        return True

    def attempt_close_instance(self, param_list: List[Param], price_list, time_list, cur_idx=None):
        if cur_idx is None:
            cur_idx = len(price_list) - 1

        # 使用int类型保持与项目一致
        cur_price = int(price_list[cur_idx])
        cur_time = time_list[cur_idx]  # 使用当前时间点
        close_attempt_instance_list = list()

        # 检查是否是交易日的收盘时间点（14:59:40或之后）
        is_end_of_trading_day = False
        if cur_time.hour == 14 and cur_time.minute == 59 and cur_time.second >= 40:
            is_end_of_trading_day = True
            print(f"交易日结束时间（14:59:40）已到，将强制平仓所有未平仓的交易。当前时间: {cur_time}")

        for param in param_list:
            for param_instance in param.instance_list:
                if not self.should_close(param_instance, param, cur_price, is_end_of_trading_day, cur_time):
                    continue

                # 设置平仓相关信息
                param_instance.actual_close_price = cur_price
                param_instance.close_time = cur_time
                param_instance.status = Status.CLOSE_ATTEMPT
                # 计算利润，考虑开仓量
                param_instance.profit = self.cal_profit(param_instance)
                # 调用执行器进行平仓，执行器会更新状态
                self.trade_executor.close(param_instance)
                # 更新资金，返还保证金并加上利润
                self.trade_executor.update_total_fund_after_close(param_instance)
                close_attempt_instance_list.append(param_instance)
        return close_attempt_instance_list

    @staticmethod
    def cal_profit(param_instance):
        if param_instance.direction == Direction.UP:
            return (param_instance.actual_close_price - param_instance.open_price) * param_instance.open_volume

        return (param_instance.open_price - param_instance.actual_close_price) * param_instance.open_volume

    @staticmethod
    def should_close(param_instance, param, cur_price, is_end_of_trading_day, cur_time):
        if param_instance.status != Status.OPEN_SUCCESS:
            return False

        if is_end_of_trading_day:
            # 交易快结束了，强制平仓
            return True

        # 获取该实例对应的参数
        param_trade_mode = param.trade_mode
        # 根据交易模式和方向判断平仓条件
        if param_trade_mode in [TradeMode.SAME_TREND_STOP_PROFIT, TradeMode.REVERSE_TREND_STOP_PROFIT]:
            # 止盈模式：价格达到或超过预期价格时平仓
            if param_instance.direction == Direction.UP and cur_price >= param_instance.expected_close_price:
                return True
            if param_instance.direction == Direction.DOWN and cur_price <= param_instance.expected_close_price:
                return True
            return False
        if param_trade_mode in [TradeMode.SAME_TREND_STOP_LOSS, TradeMode.REVERSE_TREND_STOP_LOSS]:
            # 止损模式：价格达到或低于预期价格时平仓
            if param_instance.direction == Direction.UP and cur_price <= param_instance.expected_close_price:
                return True
            if param_instance.direction == Direction.DOWN and cur_price >= param_instance.expected_close_price:
                return True
            return False
        if param_trade_mode in [TradeMode.SAME_TREND_STOP_PROFIT_WITH_37_RULE,
                                TradeMode.REVERSE_TREND_STOP_PROFIT_WITH_37_RULE]:
            # 止盈模式：价格达到或超过预期价格时平仓
            if param_instance.direction == Direction.UP and cur_price >= param_instance.expected_close_price:
                return True
            if param_instance.direction == Direction.DOWN and cur_price <= param_instance.expected_close_price:
                return True
            # 37% rule
            if cur_time <= param_instance.observe_end_time:
                # 还在观察期内，不平仓
                return False
            if param_instance.direction == Direction.UP and cur_price >= param_instance.best_price_so_far:
                return True
            if param_instance.direction == Direction.DOWN and cur_price <= param_instance.best_price_so_far:
                return True
            return False
        raise ValueError(f"尚不支持的 trade mode: {param_trade_mode}")

    @staticmethod
    def _calculate_trade_direction_and_price(param: Param, cur_price: int, actual_delta_percent: float):
        # 同向趋势模式（上涨触发看多，下跌触发看空）
        if param.trade_mode in [TradeMode.SAME_TREND_STOP_PROFIT, TradeMode.SAME_TREND_STOP_PROFIT_WITH_37_RULE,
                                TradeMode.SAME_TREND_STOP_LOSS]:
            if actual_delta_percent > 0 and actual_delta_percent >= param.delta_percent:
                # 上涨触发看多
                if param.trade_mode in [TradeMode.SAME_TREND_STOP_PROFIT,
                                        TradeMode.SAME_TREND_STOP_PROFIT_WITH_37_RULE]:
                    # 止盈：价格继续上涨到目标价格
                    return Direction.UP, int(cur_price + cur_price * param.delta_percent * param.alpha)
                # 止损：价格下跌到目标价格
                return Direction.UP, int(cur_price - cur_price * param.delta_percent * param.alpha)

            if actual_delta_percent < 0 and -actual_delta_percent >= param.delta_percent:
                # 下跌触发看空
                if param.trade_mode in [TradeMode.SAME_TREND_STOP_PROFIT,
                                        TradeMode.SAME_TREND_STOP_PROFIT_WITH_37_RULE]:
                    # 止盈：价格继续下跌到目标价格
                    return Direction.DOWN, int(cur_price - cur_price * param.delta_percent * param.alpha)
                # 止损：价格上涨到目标价格
                return Direction.DOWN, int(cur_price + cur_price * param.delta_percent * param.alpha)
            # 价格变化不满足开仓条件
            return None, None

        # 反向趋势模式（上涨触发看空，下跌触发看多）
        if param.trade_mode in [TradeMode.REVERSE_TREND_STOP_PROFIT, TradeMode.REVERSE_TREND_STOP_PROFIT_WITH_37_RULE,
                                TradeMode.REVERSE_TREND_STOP_LOSS]:
            if actual_delta_percent > 0 and actual_delta_percent >= param.delta_percent:
                # 上涨触发看空
                if param.trade_mode in [TradeMode.REVERSE_TREND_STOP_PROFIT,
                                        TradeMode.REVERSE_TREND_STOP_PROFIT_WITH_37_RULE]:
                    # 止盈：价格下跌到目标价格
                    return Direction.DOWN, int(cur_price - cur_price * param.delta_percent * param.alpha)
                # 止损：价格继续上涨到目标价格
                return Direction.DOWN, int(cur_price + cur_price * param.delta_percent * param.alpha)
            if actual_delta_percent < 0 and -actual_delta_percent >= param.delta_percent:
                # 下跌触发看多
                if param.trade_mode in [TradeMode.REVERSE_TREND_STOP_PROFIT,
                                        TradeMode.REVERSE_TREND_STOP_PROFIT_WITH_37_RULE]:
                    # 止盈：价格上涨到目标价格
                    return Direction.UP, int(cur_price + cur_price * param.delta_percent * param.alpha)
                # 止损：价格继续下跌到目标价格
                return Direction.UP, int(cur_price - cur_price * param.delta_percent * param.alpha)
            # 价格变化不满足开仓条件
            return None, None

        raise ValueError(f"尚不支持的 trade mode: {param.trade_mode}")
