import os.path

import pandas as pd
import streamlit as st

from constant.constant import *
from frontend.unit.output_unit import show_title, download_zip_file
from frontend.unit.output_util import st_stdout
from loader.file_loader import load_config
from util.common_util import is_empty
from util.date_util import get_cur_time_str
from util.file_util import mkdir_if_not_exists, is_valid_path, dump_json, get_latest_param_dir, load_json, \
    rmdir_if_exists, copy_to_dir, zip_dir, rm_if_exists, get_param_root_dir, join_path
from util.tq_util import get_code_from_tq


def meta_param(data_dir, dry_run):
    # configure
    title = "参数配置（回测模式）" if dry_run else "参数配置"
    show_title(title)
    selected_account_name = st.selectbox(label="选择账户", options=ACCOUNT_NAME_LIST)
    var_dir = join_path(data_dir, VAR_DIR, selected_account_name)
    mkdir_if_not_exists(var_dir)
    global_config_path = join_path(var_dir, GLOBAL_CONFIG_FILE_NAME)
    global_config_dict = dict()
    if is_valid_path(global_config_path):
        global_config_dict = load_json(global_config_path, with_lock=True, data_dir=data_dir)
    fund_upper_bound_last = global_config_dict[FUND_UPPER_BOUND] if FUND_UPPER_BOUND in global_config_dict else 1000000
    total_fund_for_all_instruct_last = global_config_dict[
        TOTAL_FUND_FOR_ALL_INSTRUCT] if TOTAL_FUND_FOR_ALL_INSTRUCT in global_config_dict else 1000000
    col1, col2 = st.columns([1, 1])
    with col1:
        fund_upper_bound = st.number_input(FUND_UPPER_BOUND, min_value=0, value=fund_upper_bound_last, step=100000,
                                           key="fund_upper_bound")
    with col2:
        total_fund_for_all_instruct = st.number_input(TOTAL_FUND_FOR_ALL_INSTRUCT, min_value=0,
                                                      value=total_fund_for_all_instruct_last, step=10000,
                                                      key="total_fund_for_all_instruct")
    if st.button("保存"):
        global_config_dict[FUND_UPPER_BOUND] = fund_upper_bound
        global_config_dict[TOTAL_FUND_FOR_ALL_INSTRUCT] = total_fund_for_all_instruct
        dump_json(global_config_dict, global_config_path, with_lock=True, data_dir=data_dir)
        st.success(
            f"已保存:\n\n{FUND_UPPER_BOUND}={fund_upper_bound},\n\n{TOTAL_FUND_FOR_ALL_INSTRUCT}={total_fund_for_all_instruct}（{TOTAL_FUND_FOR_ALL_INSTRUCT}需要**重启任务**生效！）")
    with st.container(border=True):
        collect_meta_param(data_dir, fund_upper_bound, dry_run, selected_account_name)
        st.button("刷新", key="flush-1")


def collect_meta_param(data_dir, fund_upper_bound, dry_run, account_name):
    meta_param_dict = dict()
    meta_param_df_dict = dict()
    param_root_dir = get_param_root_dir(data_dir, dry_run, account_name)
    mkdir_if_not_exists(param_root_dir)
    for cat in CHOOSE_CAT_LIST:
        cat_param_dir = join_path(param_root_dir, cat)
        mkdir_if_not_exists(cat_param_dir)
        param_dir = get_latest_param_dir(cat_param_dir)
        meta_param_dict_last = load_config(param_dir)
        param_version_last = meta_param_dict_last[PARAM_VERSION] if PARAM_VERSION in meta_param_dict_last else "无"
        template_version_last = meta_param_dict_last[
            TEMPLATE_VERSION] if TEMPLATE_VERSION in meta_param_dict_last else "无"
        use_cur_cat = st.checkbox(
            f"{cat}（最新版本：{param_version_last}，模板版本：{template_version_last}）",
            value=False,
            key=f"use_cur_cat-{cat}")
        if not use_cur_cat:
            continue

        meta_param_dict_last = dict()
        if is_valid_path(cat_param_dir):
            param_dir_list = os.listdir(cat_param_dir)
            param_dir_list.sort(reverse=True)
            selected_param_dir_name = st.selectbox(
                label="选择版本", options=param_dir_list, key=f"selected_param_dir-{cat}")
            if is_empty(selected_param_dir_name):
                selected_param_dir = None
            else:
                selected_param_dir = join_path(cat_param_dir, selected_param_dir_name)
            meta_param_dict_last = load_config(selected_param_dir)
            if TEMPLATE_VERSION in meta_param_dict_last:
                st.write(f"模板版本: {meta_param_dict_last[TEMPLATE_VERSION]}")
        meta_param_dict[cat] = dict()
        with st.container(border=True):
            use_api_code_last = meta_param_dict_last[USE_API_CODE] if USE_API_CODE in meta_param_dict_last else True
            use_api_code = st.checkbox("任务运行时优先使用 API 获取合约代码", value=use_api_code_last,
                                       key=f"get_api_code-{cat}")
            col1, col2 = st.columns([1, 1])
            code_last = meta_param_dict_last[CODE] if CODE in meta_param_dict_last else ""
            with col1:
                if st.button("使用 API 获取合约代码", key=f"get_code_from_tq-{cat}"):
                    with st_stdout("code"):
                        print(f"开始获取合约代码，使用账号：{account_name}")
                        code_from_tq = get_code_from_tq(data_dir, account_name, cat)
                        st.success(f"获取到的合约代码: {code_from_tq}")
            with col2:
                code = st.text_input(
                    "输入合约",
                    value=code_last,
                    key=f"code-{cat}",
                    help="大小写敏感，如果同时开启了 API 获取合约，则优先使用 API 的结果")
            col3, col4 = st.columns([1, 1])
            with col3:
                init_fund_last = meta_param_dict_last[INIT_FUND] if INIT_FUND in meta_param_dict_last else 100000
                init_fund = st.number_input(f"输入{INIT_FUND}", min_value=0, max_value=fund_upper_bound,
                                            value=init_fund_last, key=f"init_fund-{cat}")
            with col4:
                open_once_max_fund_last = meta_param_dict_last[
                    OPEN_ONCE_MAX_FUND] if OPEN_ONCE_MAX_FUND in meta_param_dict_last else 20000
                open_once_max_fund = st.number_input(f"输入{OPEN_ONCE_MAX_FUND}", min_value=0,
                                                     max_value=fund_upper_bound, value=open_once_max_fund_last,
                                                     key=f"open_once_max_fund-{cat}")
            default_meta_param = RISK_PARAM_DEFAULT_DICT[cat] if cat in RISK_PARAM_DEFAULT_DICT else RISK_PARAM_DEFAULT
            last_meta_param_df = pd.DataFrame(default_meta_param)
            if is_valid_path(selected_param_dir):
                meta_param_df_path = join_path(selected_param_dir, PARAM_DF_FILE_NAME)
                if is_valid_path(meta_param_df_path):
                    last_meta_param_df = pd.read_csv(meta_param_df_path)
                    last_meta_param_df = convert_int_column(last_meta_param_df)
            cur_meta_param_df = st.data_editor(last_meta_param_df, num_rows="dynamic", key=f"cur_param_df-{cat}",
                                               use_container_width=True)
            cur_meta_param_df = cur_meta_param_df.dropna()
            st.write(f"共 {len(cur_meta_param_df)} 行")
            cur_meta_param_df = convert_int_column(cur_meta_param_df)
            meta_param_df_dict[cat] = cur_meta_param_df
            meta_param_dict[cat] = {
                ACCOUNT: account_name,
                CAT: cat,
                CODE: code,
                # RISK_START_TIME_LIST: risk_start_time_list,
                USE_API_CODE: use_api_code,
                P_GRID: 0.2,
                INIT_FUND: init_fund,
                OPEN_ONCE_MAX_FUND: open_once_max_fund,
            }

    # save configuration
    col1, col2 = st.columns([1, 1])
    with col1:
        if st.button(f"保存{len(meta_param_dict)}个品种的配置"):
            cur_time_str = get_cur_time_str()
            version = f"{cur_time_str}_人工配置_人工生成"
            for cat in meta_param_dict:
                meta_param_dict[cat][PARAM_VERSION] = version
                param_dir = join_path(param_root_dir, cat, version)
                mkdir_if_not_exists(param_dir)
                meta_param_df_path = join_path(param_dir, PARAM_DF_FILE_NAME)
                meta_param_df_dict[cat].to_csv(meta_param_df_path, index=None)
                meta_param_path = join_path(param_dir, CONFIG_FILE_NAME)
                dump_json(meta_param_dict[cat], meta_param_path)
            st.success(f"保存成功，生成新版本：{version}")
    with col2:
        if st.button(f"打包{len(meta_param_dict)}个品种的配置"):
            export_root_dir = join_path(data_dir, EXPORT_DIR)
            export_name = get_cur_time_str()
            export_path = join_path(export_root_dir, export_name)
            rmdir_if_exists(export_path)
            for cat in meta_param_dict:
                cat_param_dir = join_path(param_root_dir, cat)
                latest_param_dir = get_latest_param_dir(cat_param_dir)
                cat_export_name = f"{cat}_{os.path.basename(latest_param_dir)}"
                copy_to_dir(latest_param_dir, export_path, cat_export_name)
            zip_path = zip_dir(export_path)
            download_zip_file(zip_path)
            rm_if_exists(zip_path)


def convert_int_column(param_df):
    param_df[TIME_RANGE] = param_df[TIME_RANGE].astype(int)
    param_df[TRADE_MODE] = param_df[TRADE_MODE].astype(int)
    return param_df
