import os

import pandas as pd
import streamlit as st
from constant.constant import ACCOUNT, ACCOUNT_FX, ACCOUNT_NAME_LIST, ACCOUNT_PARAM, AUTO_SPLIT, CAT, CHOOSE_CAT_LIST, \
    CODE, CONFIG_FILE_NAME, FUND_UPPER_BOUND, RISK_CAT_SHORT_DICT, USE_API_CODE, GLOBAL_CONFIG_FILE_NAME, INIT_FUND, \
    OPEN_ONCE_MAX_FUND, P_GRID, PARAM_DF_FILE_NAME, PARAM_TEMPLATE_DIR, PARAM_VERSION, RISK_PARAM_DEFAULT, \
    RISK_PARAM_DEFAULT_DICT, TEMPLATE_VERSION, TOTAL_FUND_FOR_ALL_INSTRUCT, VAR_DIR
from frontend.page.meta_param import convert_int_column
from frontend.unit.input_unit import checklist_with_default
from frontend.unit.output_unit import show_title
from frontend.unit.output_util import st_stdout
from loader.file_loader import load_config
from util.common_util import is_empty
from util.date_util import get_cur_time_str
from util.df_util import split_dataframe
from util.file_util import dump_json, get_latest_param_dir, get_param_root_dir, is_valid_path, join_path, load_json, \
    mkdir_if_not_exists
from util.tq_util import get_code_from_tq


def param_template(data_dir):
    title = "参数模板"
    show_title(title)
    var_dir = join_path(data_dir, VAR_DIR)
    mkdir_if_not_exists(var_dir)
    global_config_path = join_path(var_dir, GLOBAL_CONFIG_FILE_NAME)
    global_config_dict = dict()
    if is_valid_path(global_config_path):
        global_config_dict = load_json(global_config_path, with_lock=True, data_dir=data_dir)
    fund_upper_bound_last = global_config_dict[FUND_UPPER_BOUND] if FUND_UPPER_BOUND in global_config_dict else 1000000
    # total_fund_for_all_instruct_last = global_config_dict[TOTAL_FUND_FOR_ALL_INSTRUCT] if TOTAL_FUND_FOR_ALL_INSTRUCT in global_config_dict else 1000000
    col1, col2 = st.columns([1, 1])
    with col1:
        fund_upper_bound = st.number_input(FUND_UPPER_BOUND, min_value=0, value=fund_upper_bound_last, step=100000,
                                           key="fund_upper_bound")
    with col2:
        st.warning(f"每个账户的「{TOTAL_FUND_FOR_ALL_INSTRUCT}」请在「参数配置」页设置")
    if st.button("保存"):
        global_config_dict[FUND_UPPER_BOUND] = fund_upper_bound
        dump_json(global_config_dict, global_config_path, with_lock=True, data_dir=data_dir)
        st.success(f"已保存:\n\n{FUND_UPPER_BOUND}={fund_upper_bound}")
    with st.container(border=True):
        collect_param_template(data_dir, fund_upper_bound)
        st.button("刷新", key="flush-1")


def collect_param_template(data_dir, fund_upper_bound):
    template_param_dict = dict()
    meta_param_df_dict = dict()
    param_template_root_dir = join_path(data_dir, PARAM_TEMPLATE_DIR)
    mkdir_if_not_exists(param_template_root_dir)
    for cat in CHOOSE_CAT_LIST:
        cat_param_template_dir = join_path(param_template_root_dir, cat)
        mkdir_if_not_exists(cat_param_template_dir)
        param_dir = get_latest_param_dir(cat_param_template_dir)
        meta_param_dict_last = load_config(param_dir)
        param_version_last = meta_param_dict_last[PARAM_VERSION] if PARAM_VERSION in meta_param_dict_last else "无"
        use_cur_cat = st.checkbox(f"{cat}（最新版本：{param_version_last}）", value=False, key=f"use_cur_cat-{cat}")
        if not use_cur_cat:
            continue

        meta_param_dict_last = dict()
        if is_valid_path(cat_param_template_dir):
            param_dir_list = os.listdir(cat_param_template_dir)
            param_dir_list.sort(reverse=True)
            selected_param_dir_name = st.selectbox(
                label="选择版本", options=param_dir_list, key=f"selected_param_dir-{cat}")
            if is_empty(selected_param_dir_name):
                selected_param_dir = None
            else:
                selected_param_dir = join_path(cat_param_template_dir, selected_param_dir_name)
            meta_param_dict_last = load_config(selected_param_dir)
        template_param_dict[cat] = dict()
        with st.container(border=True):
            account_param_dict = dict()
            if ACCOUNT_PARAM in meta_param_dict_last:
                account_param_dict = meta_param_dict_last[ACCOUNT_PARAM]
            last_selected_account_option_list = list()
            for account_name in ACCOUNT_NAME_LIST:
                last_selected_account_option_list.append(account_name in account_param_dict)
            selected_account_name_list = checklist_with_default(
                ACCOUNT_NAME_LIST,
                default_list=last_selected_account_option_list,
                key=f"selected_account_name_list-{cat}",
                label="选择账户")

            col1, col2 = st.columns([1, 1])
            with col1:
                use_api_code_last = meta_param_dict_last[USE_API_CODE] if USE_API_CODE in meta_param_dict_last else True
                use_api_code = st.checkbox("任务运行时优先使用 API 获取合约代码", value=use_api_code_last,
                                           key=f"get_api_code-{cat}")
            with col2:
                if len(selected_account_name_list) < 2:
                    st.checkbox("开启自动均分", key=f"auto_split-{cat}", value=False, help="账号数不足，无法均分",
                                disabled=True)
                    auto_split = False
                else:
                    last_auto_split = meta_param_dict_last[AUTO_SPLIT] if AUTO_SPLIT in meta_param_dict_last else False
                    auto_split = st.checkbox("开启自动均分", key=f"auto_split-{cat}", value=last_auto_split)
            code_last = meta_param_dict_last[CODE] if CODE in meta_param_dict_last else ""
            col3, col4 = st.columns([1, 1])
            with col3:
                if st.button("使用 API 获取合约代码", key=f"get_code_from_tq-{cat}"):
                    with st_stdout("code"):
                        print(f"开始获取合约代码，使用账号：{ACCOUNT_FX}")
                        code_from_tq = get_code_from_tq(data_dir, ACCOUNT_FX, cat)
                        st.success(f"获取到的合约代码: {code_from_tq}")
            with col4:
                code = st.text_input(
                    "输入合约",
                    value=code_last,
                    key=f"code-{cat}",
                    help="大小写敏感，如果同时开启了 API 获取合约，则优先使用 API 的结果")
            for account_name in selected_account_name_list:
                st.write(f"账户-{account_name}")
                col5, col6 = st.columns([1, 1])
                with col5:
                    init_fund_last = 100000
                    if account_name in account_param_dict and INIT_FUND in account_param_dict[account_name]:
                        init_fund_last = account_param_dict[account_name][INIT_FUND]
                    init_fund = st.number_input(f"输入{INIT_FUND}", min_value=0, max_value=fund_upper_bound,
                                                value=init_fund_last, key=f"init_fund-{cat}-{account_name}")
                with col6:
                    open_once_max_fund_last = 20000
                    if account_name in account_param_dict and OPEN_ONCE_MAX_FUND in account_param_dict[account_name]:
                        open_once_max_fund_last = account_param_dict[account_name][OPEN_ONCE_MAX_FUND]
                    open_once_max_fund = st.number_input(f"输入{OPEN_ONCE_MAX_FUND}", min_value=0,
                                                         max_value=fund_upper_bound, value=open_once_max_fund_last,
                                                         key=f"open_once_max_fund-{cat}-{account_name}")
                account_param_dict[account_name] = {
                    INIT_FUND: init_fund,
                    OPEN_ONCE_MAX_FUND: open_once_max_fund
                }
            template_param_dict[cat] = {
                AUTO_SPLIT: auto_split,
                ACCOUNT_PARAM: account_param_dict,
                CAT: cat,
                CODE: code,
                USE_API_CODE: use_api_code,
                P_GRID: 0.2,
            }
            default_meta_param = RISK_PARAM_DEFAULT_DICT[cat] if cat in RISK_PARAM_DEFAULT_DICT else RISK_PARAM_DEFAULT
            last_meta_param_df = pd.DataFrame(default_meta_param)
            if is_valid_path(selected_param_dir):
                meta_param_df_path = join_path(selected_param_dir, PARAM_DF_FILE_NAME)
                if is_valid_path(meta_param_df_path):
                    last_meta_param_df = pd.read_csv(meta_param_df_path)
                    last_meta_param_df = convert_int_column(last_meta_param_df)
            cur_meta_param_df = st.data_editor(last_meta_param_df, num_rows="dynamic", key=f"cur_param_df-{cat}",
                                               use_container_width=True)
            cur_meta_param_df = cur_meta_param_df.dropna()
            st.write(f"共 {len(cur_meta_param_df)} 行")
            cur_meta_param_df = convert_int_column(cur_meta_param_df)
            meta_param_df_dict[cat] = cur_meta_param_df
    # save configuration
    col1, col2 = st.columns([1, 1])
    with col1:
        if st.button(f"保存{len(template_param_dict)}个品种的配置", disabled=is_empty(template_param_dict)):
            # ToDo(hm): need config validation？
            save_param_template(param_template_root_dir, template_param_dict, meta_param_df_dict)
    with col2:
        if st.button("保存并随机均分成多份", disabled=is_empty(template_param_dict)):
            template_version = save_param_template(param_template_root_dir, template_param_dict, meta_param_df_dict)
            for cat in template_param_dict:
                # split param_df and param_dict
                cur_param_df = meta_param_df_dict[cat]
                split_num = len(template_param_dict[cat][ACCOUNT_PARAM])
                if split_num < 2:
                    st.warning(f"品种-{cat} 账号数不足({split_num}<2)，无法均分")
                    continue

                split_param_df_list = split_dataframe(cur_param_df, split_num)
                for idx, account_name in enumerate(template_param_dict[cat][ACCOUNT_PARAM].keys()):
                    param_root_dir = get_param_root_dir(data_dir, False, account_name)
                    version = f"{template_version}_模板配置_人工生成"
                    param_dir = join_path(param_root_dir, cat, version)
                    mkdir_if_not_exists(param_dir)
                    meta_param_df_path = join_path(param_dir, PARAM_DF_FILE_NAME)
                    split_param_df_list[idx].to_csv(meta_param_df_path, index=None)
                    splited_param_dict = {
                        TEMPLATE_VERSION: template_version,
                        PARAM_VERSION: version,
                        ACCOUNT: account_name,
                        CAT: cat,
                        CODE: template_param_dict[cat][CODE],
                        USE_API_CODE: template_param_dict[cat][USE_API_CODE],
                        P_GRID: template_param_dict[cat][P_GRID],
                        INIT_FUND: template_param_dict[cat][ACCOUNT_PARAM][account_name][INIT_FUND],
                        OPEN_ONCE_MAX_FUND: template_param_dict[cat][ACCOUNT_PARAM][account_name][OPEN_ONCE_MAX_FUND],
                    }
                    meta_param_path = join_path(param_dir, CONFIG_FILE_NAME)
                    dump_json(splited_param_dict, meta_param_path)
                    st.success(f"保存成功，生成新参数，品种-{cat} 账号-{account_name} 版本-{version}")


def save_param_template(param_template_root_dir, meta_param_dict, meta_param_df_dict):
    version = get_cur_time_str()
    for cat in meta_param_dict:
        meta_param_dict[cat][PARAM_VERSION] = version
        param_dir = join_path(param_template_root_dir, cat, version)
        mkdir_if_not_exists(param_dir)
        meta_param_df_path = join_path(param_dir, PARAM_DF_FILE_NAME)
        meta_param_df_dict[cat].to_csv(meta_param_df_path, index=None)
        meta_param_path = join_path(param_dir, CONFIG_FILE_NAME)
        dump_json(meta_param_dict[cat], meta_param_path)
    st.success(f"保存成功，生成新模板：{version}")
    return version
