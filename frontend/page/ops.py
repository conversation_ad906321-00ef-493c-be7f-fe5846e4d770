import getpass
import multiprocessing
import os
import socket

import pandas as pd
import streamlit as st

from constant.constant import VAR_DIR, WORD_FILE_NAME, CONFIG_DIR, ACCOUNT_KEY_LIST, ACCOUNT_CONFIG_FILE_NAME, BROKER, \
    SYSTEM_RUN_LOG_NAME, CODE, CZCE_SR, ACCOUNT_NAME_LIST, GLOBAL_ACCOUNT_CONFIG_FILE_NAME, G<PERSON><PERSON>BAL_ACCOUNT_KEY_LIST
from frontend.unit.output_unit import show_run_log, show_title
from frontend.unit.output_util import st_stdout
from loader.file_loader import load_config
from util.common_util import get_code_version, git_pull, is_empty
from util.email_util import send_info_email
from util.encrypt_util import generate_readable_password, load_word_line_list, encrypt_simple, encrypt, decrypt, \
    is_valid_encrypt_process
from util.file_util import update_json_file, get_param_root_dir, get_latest_param_dir, is_invalid_path, \
    mkdir_if_not_exists, join_path
from util.process_util import get_cur_process_start_time, kill_process_and_children, \
    get_child_process_count, get_process_info_list
from util.tq_util import get_tq_api


def ops(data_dir, project_dir, branch_name, authenticator):
    with st.container(border=True):
        ops_action(data_dir, project_dir, branch_name)
    try:
        fields = {'Form name': '重置页面登录密码', 'Current password': '当前密码', 'New password': '新密码',
                  'Repeat password': '重复新密码', 'Reset': '重置'}
        if authenticator.reset_password(st.session_state['username'], fields=fields):
            st.success('Password modified successfully')
    except Exception as e:
        st.error(e)
    show_title("随机数")
    with st.container(border=True):
        gen_passwd(project_dir)
    show_title("更新全局信息")
    with st.container(border=True):
        update_global_account_config(data_dir)
        test_send_msg(data_dir, project_dir)
    show_title("更新账户信息")
    with st.container(border=True):
        selected_account_name = st.selectbox(label="选择账户", options=ACCOUNT_NAME_LIST)
        update_account_config(data_dir, selected_account_name)
        test_account(data_dir, selected_account_name)


def gen_passwd(project_dir):
    password_len = st.number_input("密码长度", min_value=1, value=10)
    if st.button("生成密码"):
        password_list = list()
        word_file_path = join_path(project_dir, CONFIG_DIR, WORD_FILE_NAME)
        word_line_list = load_word_line_list(word_file_path)
        for name in ["pg"]:
            password, explain = generate_readable_password(password_len, word_line_list)
            is_valid = is_valid_encrypt_process(password, encrypt, decrypt)
            password_list.append(
                {"合法": is_valid, "名称": name, "密码": password, "解释": explain})
        for name in ["dl"]:
            password, explain = generate_readable_password(password_len, word_line_list, add_special=True)
            password_list.append(
                {"合法": True, "名称": name, "密码": password, "解释": explain})
        password_df = pd.DataFrame(password_list)
        st.dataframe(password_df, hide_index=True, use_container_width=True)


def update_global_account_config(data_dir):
    col1, col2 = st.columns([1, 2])
    with col1:
        key = st.selectbox(label="键", options=GLOBAL_ACCOUNT_KEY_LIST, key="update_global_account_config-key")
    with col2:
        val = st.text_input(label="值", key="update_global_account_config-val")
    if st.button("更新", key="update_global_account_config-update"):
        if is_empty(val):
            st.error(f"更新失败：输入的值{val}为空")
            return

        var_dir = join_path(data_dir, VAR_DIR)
        mkdir_if_not_exists(var_dir)
        account_config_path = join_path(var_dir, GLOBAL_ACCOUNT_CONFIG_FILE_NAME)
        val_en = encrypt(val)
        update_json_file(account_config_path, {key: val_en})
        st.success(f"更新全局账户信息成功，{key}:{val}")


def update_account_config(data_dir, account_name):
    col1, col2 = st.columns([1, 2])
    with col1:
        key = st.selectbox(label="键", options=ACCOUNT_KEY_LIST, key="update_account_config-key")
    with col2:
        val = st.text_input(label="值", key="update_account_config-val")
    if st.button("更新", key="update_account_config-update"):
        if is_empty(val):
            st.error(f"更新失败：输入的值{val}为空")
            return

        var_dir = join_path(data_dir, VAR_DIR, account_name)
        mkdir_if_not_exists(var_dir)
        account_config_path = join_path(var_dir, ACCOUNT_CONFIG_FILE_NAME)
        if key == BROKER:
            val_en = encrypt_simple(val)
        else:
            val_en = encrypt(val)
        update_json_file(account_config_path, {key: val_en})
        st.success(f"更新{account_name}的账户信息成功，{key}:{val}")


def test_account(data_dir, account_name):
    code = st.text_input("指定合约代码", value=get_test_code(data_dir, account_name))
    if st.button("测试账号登录"):
        api = None
        with st_stdout("code"):
            try:
                api = get_tq_api(data_dir, account_name)
                account = api.get_account()
                quote = api.get_quote(code)
                st.success(
                    f"获取账号信息成功: currency={account.currency}, frozen_commission={account.frozen_commission}")
                st.success(f"获取合约信息成功: {quote}")
            except Exception as e:
                st.error(f"测试账号登录失败: {e}")
            finally:
                if api is not None:
                    api.close()


def get_test_code(data_dir, account_name):
    default_code = "CZCE.SR501"
    cat_param_dir = join_path(get_param_root_dir(data_dir, False, account_name), CZCE_SR)
    meta_param_path = get_latest_param_dir(cat_param_dir)
    if is_invalid_path(meta_param_path):
        return default_code

    meta_param = load_config(meta_param_path)
    if is_empty(meta_param) or CODE not in meta_param:
        return default_code

    return meta_param[CODE]


def ops_action(data_dir, project_dir, branch_name):
    info_dict = {
        "代码版本": get_code_version(project_dir),
        "数据目录": data_dir,
        "运行环境": f"{getpass.getuser()}@{socket.gethostname()}",
        "cpu 数量": multiprocessing.cpu_count(),
        "airisk 进程列表": get_process_info_list("airisk/bin/python"),
        "当前子进程数": get_child_process_count(os.getpid()),
        "系统启动时间": get_cur_process_start_time()
    }
    st.json(info_dict)
    st.write("*系统空闲的时候至少有 3 个进程：(1) streamlit，(2) main，(3) spawn_main（guard task）")
    col1, col2 = st.columns([1, 1])
    with col1:
        branch_name_in = st.text_input("分支", value=branch_name, key="branch_name_in")
    with col2:
        tag_in = st.text_input("tag", value="", key="tag_in")
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        if is_empty(branch_name_in):
            st.warning("输入正确的分支信息")
        else:
            version_info = f"{branch_name_in} 分支" if is_empty(tag_in) else f"{branch_name_in} 分支，tag:{tag_in}"
            if st.button(f"拉取最新代码（{version_info}）"):
                # ToDo(hm): pip install -r when pull in ops?
                git_pull(project_dir, branch_name_in, tag_in)
                st.session_state.clear()
    with col2:
        st.button("刷新页面")
    with col3:
        if st.button("重启系统", type="primary"):
            kill_process_and_children(os.getpid())
    log_path = join_path(data_dir, VAR_DIR, SYSTEM_RUN_LOG_NAME)
    show_run_log(log_path)


def test_send_msg(data_dir, project_dir):
    if st.button("测试通知"):
        content = f"代码版本: {get_code_version(project_dir)}"
        with st_stdout("code"):
            send_info_email(data_dir, "通知测试", "页面手动触发，" + content)
